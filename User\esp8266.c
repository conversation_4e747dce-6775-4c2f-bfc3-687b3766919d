#include "sys.h"
#include "delay.h"
#include "../Drivers/SYSTEM/usart/usart.h"
#include "esp8266.h"
#include "pinconfig.h"
#include <string.h>
#include <stdio.h>

extern uint32_t HAL_GetTick(void);

#define ESP8266_RX_BUFFER_SIZE 512
static uint8_t esp8266_rx_buffer[ESP8266_RX_BUFFER_SIZE];
static uint16_t esp8266_rx_count = 0;

/* 错误状态定义 */
#define ESP8266_ERROR_NONE        0   /* 无错误 */
#define ESP8266_ERROR_TIMEOUT     1   /* 超时错误 */
#define ESP8266_ERROR_CONNECT     2   /* 连接错误 */
#define ESP8266_ERROR_SEND        3   /* 发送错误 */
#define ESP8266_ERROR_MQTT        4   /* MQTT错误 */

/* 错误状态 */
static uint8_t g_esp8266_error = ESP8266_ERROR_NONE;
static uint8_t g_retry_count = 0;
static uint8_t g_max_retries = 3;

/* MQTT连接状态 */
static uint8_t g_mqtt_connected = 0;
static uint32_t g_last_ping_time = 0;

/* 获取最近的错误状态 */
uint8_t esp8266_get_error(void)
{
    return g_esp8266_error;
}

/* 清除错误状态 */
void esp8266_clear_error(void)
{
    g_esp8266_error = ESP8266_ERROR_NONE;
    g_retry_count = 0;
}

/* 设置最大重试次数 */
void esp8266_set_max_retries(uint8_t max_retries)
{
    g_max_retries = max_retries;
}

void esp8266_rx_callback(uint8_t data)
{
    if (esp8266_rx_count < ESP8266_RX_BUFFER_SIZE - 1)
    {
        esp8266_rx_buffer[esp8266_rx_count++] = data;
    }
}

static uint8_t esp8266_send_cmd(char *cmd, char *ack, uint16_t timeout)
{
    uint8_t result = 0;
    uint16_t retry_timeout = timeout;
    
    memset(esp8266_rx_buffer, 0, ESP8266_RX_BUFFER_SIZE);
    esp8266_rx_count = 0;
    
    /* 通过USART3发送命令 */
    char *p = cmd;
    while (*p)
    {
        while (!(ESP8266_USART->ISR & (1 << 7)));
        ESP8266_USART->TDR = *p++;
    }
    while (!(ESP8266_USART->ISR & (1 << 7)));
    ESP8266_USART->TDR = '\r';
    while (!(ESP8266_USART->ISR & (1 << 7)));
    ESP8266_USART->TDR = '\n';
    
    if (ack != NULL)
    {
        while (retry_timeout--)
        {
            delay_ms(1);
            
            if (strstr((char *)esp8266_rx_buffer, ack) != NULL)
            {
                result = 1;
                break;
            }
            
            /* 检查常见错误响应 */
            if (strstr((char *)esp8266_rx_buffer, "ERROR") != NULL)
            {
                g_esp8266_error = ESP8266_ERROR_CONNECT;
                break;
            }
            
            if (strstr((char *)esp8266_rx_buffer, "FAIL") != NULL)
            {
                g_esp8266_error = ESP8266_ERROR_CONNECT;
                break;
            }
        }
        
        if (retry_timeout == 0 && result == 0)
        {
            g_esp8266_error = ESP8266_ERROR_TIMEOUT;
        }
    }
    
    return result;
}

static void esp8266_uart_init(void)
{
    ESP8266_TX_GPIO_CLK;
    ESP8266_RX_GPIO_CLK;
    ESP8266_USART_CLK_ENABLE();

    sys_gpio_set(ESP8266_TX_GPIO_PORT, 1 << ESP8266_TX_PIN,
                 SYS_GPIO_MODE_AF, SYS_GPIO_OTYPE_PP, SYS_GPIO_SPEED_HIGH, SYS_GPIO_PUPD_PU);

    sys_gpio_set(ESP8266_RX_GPIO_PORT, 1 << ESP8266_RX_PIN,
                 SYS_GPIO_MODE_AF, SYS_GPIO_OTYPE_PP, SYS_GPIO_SPEED_HIGH, SYS_GPIO_PUPD_PU);

    sys_gpio_af_set(ESP8266_TX_GPIO_PORT, ESP8266_TX_PIN, 7);
    sys_gpio_af_set(ESP8266_RX_GPIO_PORT, ESP8266_RX_PIN, 7);
    
    ESP8266_USART->CR1 = 0;
    ESP8266_USART->CR1 |= 1 << 0;
    ESP8266_USART->CR1 |= 1 << 2;
    ESP8266_USART->CR1 |= 1 << 5;

    /* 配置波特率 - 基于130MHz PCLK1时钟 */
    uint32_t temp = (130 * 1000000 + 115200 / 2) / 115200;
    ESP8266_USART->BRR = temp;

    ESP8266_USART->CR1 |= 1 << 0;
    
    NVIC_SetPriority(USART3_IRQn, 3);
    NVIC_EnableIRQ(USART3_IRQn);
}

void USART3_IRQHandler(void)
{
    uint8_t res;

    if (ESP8266_USART->ISR & (1 << 5))
    {
        res = ESP8266_USART->RDR;

        if (esp8266_rx_count < ESP8266_RX_BUFFER_SIZE - 1)
        {
            esp8266_rx_buffer[esp8266_rx_count++] = res;
        }
    }
}

void esp8266_init(void)
{
    esp8266_uart_init();
    
    delay_ms(1000);
    
    /* 清除错误状态 */
    esp8266_clear_error();
    
    /* 尝试AT命令，确认模块通信正常 */
    if (!esp8266_send_cmd("AT", "OK", 1000))
    {
        g_esp8266_error = ESP8266_ERROR_CONNECT;
        return;
    }
    
    /* 设置为Station模式 */
    esp8266_send_cmd("AT+CWMODE=1", "OK", 1000);
    
    /* 关闭回显 */
    esp8266_send_cmd("ATE0", "OK", 1000);
}

uint8_t esp8266_connect_ap(void)
{
    char cmd[128];  /* 增大缓冲区以容纳更长的SSID和密码 */
    uint8_t result = 0;
    
    /* 清除错误状态 */
    esp8266_clear_error();
    
    /* 构建连接命令 */
    snprintf(cmd, sizeof(cmd), "AT+CWJAP=\"%s\",\"%s\"", WIFI_SSID, WIFI_PASSWORD);
    
    /* 尝试连接AP，最多重试g_max_retries次 */
    for (g_retry_count = 0; g_retry_count < g_max_retries; g_retry_count++)
    {
        result = esp8266_send_cmd(cmd, "WIFI GOT IP", 20000);
        if (result)
        {
            /* 连接成功 */
            break;
        }
        
        /* 连接失败，等待一段时间后重试 */
        delay_ms(1000);
        
        /* 如果是超时错误，可以继续重试；如果是其他错误，直接返回 */
        if (g_esp8266_error != ESP8266_ERROR_TIMEOUT)
        {
            break;
        }
    }
    
    if (!result)
    {
        g_esp8266_error = ESP8266_ERROR_CONNECT;
    }
    else
    {
        g_esp8266_error = ESP8266_ERROR_NONE;
    }
    
    return result;
}

uint8_t esp8266_connect_mqtt(void)
{
    char cmd[128];
    uint8_t result = 0;
    
    /* 清除错误状态 */
    esp8266_clear_error();
    g_mqtt_connected = 0;

    /* 关闭之前的连接 */
    esp8266_send_cmd("AT+CIPCLOSE", "OK", 1000);
    
    /* 设置单连接模式 */
    esp8266_send_cmd("AT+CIPMUX=0", "OK", 1000);
    
    /* 尝试连接MQTT服务器，最多重试g_max_retries次 */
    for (g_retry_count = 0; g_retry_count < g_max_retries; g_retry_count++)
    {
        /* 建立TCP连接 */
        snprintf(cmd, sizeof(cmd), "AT+CIPSTART=\"TCP\",\"%s\",%s", MQTT_HOST, MQTT_PORT);
        if (!esp8266_send_cmd(cmd, "CONNECT", 5000))
        {
            /* 连接失败，等待一段时间后重试 */
            delay_ms(1000);
            continue;
        }
        
        /* 准备MQTT连接数据包 */
        char connect_packet[256];
        uint16_t packet_len = 0;
        
        /* 计算长度 */
        uint16_t client_id_len = strlen(MQTT_CLIENT_ID);
        uint16_t username_len = strlen(MQTT_USERNAME);
        uint16_t password_len = strlen(MQTT_PASSWORD);
        uint16_t remaining_len = 10 + 2 + client_id_len + 2 + username_len + 2 + password_len;

        /* 固定头部 */
        connect_packet[packet_len++] = 0x10;  /* CONNECT消息类型 */

        /* 剩余长度编码 */
        if (remaining_len < 128) {
            connect_packet[packet_len++] = remaining_len;
        } else {
            connect_packet[packet_len++] = (remaining_len & 0x7F) | 0x80;
            connect_packet[packet_len++] = (remaining_len >> 7) & 0x7F;
        }

        /* 协议名 */
        connect_packet[packet_len++] = 0x00;
        connect_packet[packet_len++] = 0x04;
        connect_packet[packet_len++] = 'M';
        connect_packet[packet_len++] = 'Q';
        connect_packet[packet_len++] = 'T';
        connect_packet[packet_len++] = 'T';

        /* 协议级别 */
        connect_packet[packet_len++] = 0x04;

        /* 连接标志 */
        connect_packet[packet_len++] = 0xC2;

        /* 保持连接时间 */
        connect_packet[packet_len++] = 0x00;
        connect_packet[packet_len++] = 0x78;

        /* 负载 - Client ID */
        connect_packet[packet_len++] = client_id_len >> 8;
        connect_packet[packet_len++] = client_id_len & 0xFF;
        memcpy(&connect_packet[packet_len], MQTT_CLIENT_ID, client_id_len);
        packet_len += client_id_len;

        /* 负载 - Username */
        connect_packet[packet_len++] = username_len >> 8;
        connect_packet[packet_len++] = username_len & 0xFF;
        memcpy(&connect_packet[packet_len], MQTT_USERNAME, username_len);
        packet_len += username_len;

        /* 负载 - Password */
        connect_packet[packet_len++] = password_len >> 8;
        connect_packet[packet_len++] = password_len & 0xFF;
        memcpy(&connect_packet[packet_len], MQTT_PASSWORD, password_len);
        packet_len += password_len;
        
        /* 发送MQTT连接数据包 */
        snprintf(cmd, sizeof(cmd), "AT+CIPSEND=%d", packet_len);
        if (!esp8266_send_cmd(cmd, ">", 1000))
        {
            /* 发送失败，等待一段时间后重试 */
            delay_ms(1000);
            continue;
        }
        
        /* 发送数据包 */
        for (uint16_t i = 0; i < packet_len; i++)
        {
            ESP8266_UART->TDR = connect_packet[i];
            while (!(ESP8266_UART->ISR & (1 << 6)));
        }
        
        delay_ms(2000);
        
        /* 检查MQTT连接响应 - CONNACK */
        if (strstr((char *)esp8266_rx_buffer, "SEND OK") != NULL)
        {
            /* 等待CONNACK响应 */
            delay_ms(1000);

            /* 简化检查：如果发送成功且没有错误，认为连接成功 */
            if (strstr((char *)esp8266_rx_buffer, "ERROR") == NULL)
            {
                result = 1;
                g_mqtt_connected = 1;
                g_esp8266_error = ESP8266_ERROR_NONE;
                g_last_ping_time = HAL_GetTick();
                break;
            }
        }

        g_esp8266_error = ESP8266_ERROR_MQTT;
        delay_ms(1000);
    }
    
    return result;
}

uint8_t esp8266_send_data(float lat, float lon)
{
    char data[128];  /* 增大缓冲区以容纳更多数据 */
    char cmd[128];   /* 增大缓冲区 */
    uint8_t result = 0;
    
    /* 清除错误状态 */
    esp8266_clear_error();
    
    /* 构建JSON数据 */
    snprintf(data, sizeof(data), "{\"latitude\":%.6f,\"longitude\":%.6f}", lat, lon);
    
    /* 尝试发送数据，最多重试g_max_retries次 */
    for (g_retry_count = 0; g_retry_count < g_max_retries; g_retry_count++)
    {
        /* 准备发送数据 */
        snprintf(cmd, sizeof(cmd), "AT+CIPSEND=%d", strlen(data));
        
        if (esp8266_send_cmd(cmd, ">", 1000))
        {
            if (esp8266_send_cmd(data, "SEND OK", 5000))
            {
                result = 1;
                g_esp8266_error = ESP8266_ERROR_NONE;
                break;
            }
        }
        
        /* 发送失败，等待一段时间后重试 */
        g_esp8266_error = ESP8266_ERROR_SEND;
        delay_ms(1000);
    }
    
    return result;
}

uint8_t esp8266_mqtt_publish(float lat, float lon)
{
    char cmd[128];
    char payload[256];  /* 增大缓冲区以容纳更多数据 */
    uint8_t result = 0;
    
    /* 清除错误状态 */
    esp8266_clear_error();
    
    /* 构建JSON负载 */
    snprintf(payload, sizeof(payload), 
             "{\"params\":{\"latitude\":%.6f,\"longitude\":%.6f,\"status\":\"defect_detected\"}}", 
             lat, lon);
    
    /* 尝试发布消息，最多重试g_max_retries次 */
    for (g_retry_count = 0; g_retry_count < g_max_retries; g_retry_count++)
    {
        char publish_packet[512];  /* 增大缓冲区 */
        uint16_t packet_len = 0;
        uint16_t payload_len = strlen(payload);
        uint16_t topic_len = strlen(MQTT_PUBLISH_TOPIC);
        
        /* 构建MQTT发布数据包 */
        publish_packet[packet_len++] = 0x30;
        
        uint16_t remaining_len = 2 + topic_len + payload_len;
        
        if (remaining_len < 128)
        {
            publish_packet[packet_len++] = remaining_len;
        }
        else
        {
            publish_packet[packet_len++] = (remaining_len & 0x7F) | 0x80;
            publish_packet[packet_len++] = remaining_len >> 7;
        }
        
        publish_packet[packet_len++] = topic_len >> 8;
        publish_packet[packet_len++] = topic_len & 0xFF;
        
        memcpy(&publish_packet[packet_len], MQTT_PUBLISH_TOPIC, topic_len);
        packet_len += topic_len;
        
        memcpy(&publish_packet[packet_len], payload, payload_len);
        packet_len += payload_len;
        
        /* 准备发送数据 */
        snprintf(cmd, sizeof(cmd), "AT+CIPSEND=%d", packet_len);
        if (!esp8266_send_cmd(cmd, ">", 1000))
        {
            g_esp8266_error = ESP8266_ERROR_SEND;
            delay_ms(1000);
            continue;
        }
        
        /* 发送数据包 */
        for (uint16_t i = 0; i < packet_len; i++)
        {
            ESP8266_USART->TDR = publish_packet[i];
            while (!(ESP8266_USART->ISR & (1 << 6)));
        }
        
        delay_ms(1000);
        
        /* 检查发送结果 */
        if (strstr((char *)esp8266_rx_buffer, "SEND OK") != NULL)
        {
            result = 1;
            g_esp8266_error = ESP8266_ERROR_NONE;
            break;
        }
        else
        {
            g_esp8266_error = ESP8266_ERROR_SEND;
            delay_ms(1000);
        }
    }
    
    return result;
}

uint8_t esp8266_mqtt_publish_defect(float lat, float lon, uint8_t defect_type, uint8_t confidence, uint32_t defect_count)
{
    char cmd[128];
    char payload[512];
    uint8_t result = 0;

    esp8266_clear_error();

    /* 构建阿里云IoT平台标准JSON格式 */
    const char* defect_type_names[] = {"none", "yashang", "sunshang", "other"};
    const char* type_name = (defect_type < 4) ? defect_type_names[defect_type] : "unknown";
    uint32_t timestamp = HAL_GetTick();

    snprintf(payload, sizeof(payload),
             "{\"id\":\"%lu\",\"version\":\"1.0\",\"params\":{\"latitude\":{\"value\":%.6f,\"time\":%lu},\"longitude\":{\"value\":%.6f,\"time\":%lu},\"defect_type\":{\"value\":\"%s\",\"time\":%lu},\"confidence\":{\"value\":%d,\"time\":%lu},\"defect_count\":{\"value\":%lu,\"time\":%lu}},\"method\":\"thing.event.property.post\"}",
             timestamp, lat, timestamp, lon, timestamp, type_name, timestamp, confidence, timestamp, defect_count, timestamp);

    /* 尝试发布消息 */
    for (g_retry_count = 0; g_retry_count < g_max_retries; g_retry_count++)
    {
        char publish_packet[512];
        uint16_t packet_len = 0;
        uint16_t payload_len = strlen(payload);
        uint16_t topic_len = strlen(MQTT_PUBLISH_TOPIC);

        /* 构建MQTT PUBLISH包 */
        uint16_t remaining_len = topic_len + payload_len + 2;

        /* 固定头部 */
        publish_packet[packet_len++] = 0x30;  /* PUBLISH消息类型，QoS=0 */

        /* 剩余长度编码 */
        if (remaining_len < 128) {
            publish_packet[packet_len++] = remaining_len;
        } else {
            publish_packet[packet_len++] = (remaining_len & 0x7F) | 0x80;
            publish_packet[packet_len++] = (remaining_len >> 7) & 0x7F;
        }

        /* 可变头部 - 主题长度 */
        publish_packet[packet_len++] = (topic_len >> 8) & 0xFF;
        publish_packet[packet_len++] = topic_len & 0xFF;

        /* 可变头部 - 主题 */
        memcpy(&publish_packet[packet_len], MQTT_PUBLISH_TOPIC, topic_len);
        packet_len += topic_len;

        /* 负载 - JSON数据 */
        memcpy(&publish_packet[packet_len], payload, payload_len);
        packet_len += payload_len;

        /* 发送数据 */
        snprintf(cmd, sizeof(cmd), "AT+CIPSEND=%d", packet_len);
        if (!esp8266_send_cmd(cmd, ">", 1000))
        {
            g_esp8266_error = ESP8266_ERROR_SEND;
            delay_ms(1000);
            continue;
        }

        /* 发送MQTT包 */
        for (uint16_t i = 0; i < packet_len; i++)
        {
            ESP8266_USART->TDR = publish_packet[i];
            while (!(ESP8266_USART->ISR & (1 << 6)));
        }

        delay_ms(1000);

        if (strstr((char *)esp8266_rx_buffer, "SEND OK") != NULL)
        {
            result = 1;
            g_esp8266_error = ESP8266_ERROR_NONE;
            break;
        }
        else
        {
            g_esp8266_error = ESP8266_ERROR_SEND;
            delay_ms(1000);
        }
    }

    return result;
}

uint8_t esp8266_is_mqtt_connected(void)
{
    /* 检查连接超时 */
    uint32_t current_time = HAL_GetTick();
    if (g_mqtt_connected && (current_time - g_last_ping_time > 120000))
    {
        g_mqtt_connected = 0;
        g_esp8266_error = ESP8266_ERROR_TIMEOUT;
    }

    return g_mqtt_connected;
}



uint8_t esp8266_mqtt_ping(void)
{
    char cmd[64];
    uint8_t ping_packet[2] = {0xC0, 0x00};  /* PINGREQ包 */
    uint8_t result = 0;

    /* 发送PING包 */
    snprintf(cmd, sizeof(cmd), "AT+CIPSEND=%d", 2);
    if (esp8266_send_cmd(cmd, ">", 1000))
    {
        /* 发送PINGREQ */
        for (int i = 0; i < 2; i++)
        {
            ESP8266_USART->TDR = ping_packet[i];
            while (!(ESP8266_USART->ISR & (1 << 6)));
        }

        delay_ms(1000);

        /* 检查PINGRESP */
        if (strstr((char *)esp8266_rx_buffer, "SEND OK") != NULL)
        {
            result = 1;
            g_last_ping_time = HAL_GetTick();
        }
    }

    return result;
}

static void esp8266_send_buffer(const uint8_t *data, uint16_t len)
{
    for (uint16_t i = 0; i < len; i++)
    {
        ESP8266_USART->TDR = data[i];
        while (!(ESP8266_USART->ISR & (1 << 6)));
    }
}

void esp8266_send_at_cmd(const char *cmd)
{
    esp8266_send_buffer((uint8_t *)cmd, strlen(cmd));
    esp8266_send_buffer((uint8_t *)"\r\n", 2);
} 

