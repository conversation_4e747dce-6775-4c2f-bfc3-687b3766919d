#ifndef __ESP8266_H
#define __ESP8266_H

#include "sys.h"
#include "pinconfig.h"

/* 使用pinconfig.h中的定义 */
#define ESP8266_UART          ESP8266_USART
#define ESP8266_UART_TX_GPIO  ESP8266_TX_GPIO_PORT
#define ESP8266_UART_TX_PIN   (1 << ESP8266_TX_PIN)
#define ESP8266_UART_RX_GPIO  ESP8266_RX_GPIO_PORT
#define ESP8266_UART_RX_PIN   (1 << ESP8266_RX_PIN)

#define WIFI_SSID            "V831"
#define WIFI_PASSWORD        "147369258"

#define MQTT_HOST            "a1lod4v9cR.iot-as-mqtt.cn-shanghai.aliyuncs.com"
#define MQTT_PORT            "1883"
#define MQTT_PRODUCT_KEY     "a1lod4v9cR"
#define MQTT_DEVICE_NAME     "0001"
#define MQTT_DEVICE_SECRET   "4824906d4bf83b55059dc686f9ec023fc2d116473d4016dc4193d9d6c3752"
#define MQTT_CLIENT_ID       "a1lod4v9cR.0001|securemode=2,signmethod=hmacsha256,timestamp=1753179480170"
#define MQTT_USERNAME        "0001&a1lod4v9cR"
#define MQTT_PASSWORD        "4824906d4bf83b55059dc686f9ec023fc2d116473d4016dc4193d9d6c3752"
#define MQTT_PUBLISH_TOPIC   "/a1lod4v9cR/0001/user/update"
#define MQTT_SUBSCRIBE_TOPIC "/a1lod4v9cR/0001/user/get"

/* 错误状态定义 */
#define ESP8266_ERROR_NONE        0   /* 无错误 */
#define ESP8266_ERROR_TIMEOUT     1   /* 超时错误 */
#define ESP8266_ERROR_CONNECT     2   /* 连接错误 */
#define ESP8266_ERROR_SEND        3   /* 发送错误 */
#define ESP8266_ERROR_MQTT        4   /* MQTT错误 */

void esp8266_init(void);
uint8_t esp8266_connect_ap(void);
uint8_t esp8266_connect_mqtt(void);
uint8_t esp8266_send_data(float lat, float lon);
uint8_t esp8266_mqtt_publish(float lat, float lon);
uint8_t esp8266_mqtt_publish_defect(float lat, float lon, uint8_t defect_type, uint8_t confidence, uint32_t defect_count);
uint8_t esp8266_mqtt_ping(void);
uint8_t esp8266_is_mqtt_connected(void);
void esp8266_rx_callback(uint8_t data);

/* 错误处理函数 */
uint8_t esp8266_get_error(void);
void esp8266_clear_error(void);
void esp8266_set_max_retries(uint8_t max_retries);

#endif

